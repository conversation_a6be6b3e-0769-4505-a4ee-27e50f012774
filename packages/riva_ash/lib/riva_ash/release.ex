defmodule RivaAsh.Release do
  @moduledoc """
  Used for executing DB release tasks when run in production without Mix
  installed as a dependency.
  """
  @app :riva_ash
  def migrate do
    with :ok <- load_app(),
         {:ok, repos} <- repos() do
      results = Enum.map(repos, fn repo ->
        case Ecto.Migrator.with_repo(repo, &Ecto.Migrator.run(&1, :up, all: true)) do
          {status, _, _} -> {status, repo}
          error -> error
        end
      end)
      {:ok, results}
    else
      error -> error
    end
  end

  def rollback(repo, version) do
    with :ok <- load_app() do
      Ecto.Migrator.with_repo(repo, &Ecto.Migrator.run(&1, :down, to: version))
    end
  end

  defp repos do
    case Application.fetch_env(@app, :ecto_repos) do
      {:ok, repos} -> {:ok, repos}
      :error -> {:error, :ecto_repos_not_configured}
    end
  end

  defp load_app do
    case Application.load(@app) do
      :ok -> :ok
      {:error, reason} -> {:error, reason}
    end
  end
end
