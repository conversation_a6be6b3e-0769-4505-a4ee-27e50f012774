defmodule RivaAsh.ResultHelpers do
  @moduledoc """
  Provides common result handling functions and operators.
  This module should be imported in any module that needs to use the OK library's
  result handling functions and operators.
  """
  
  @doc """
  Imports and aliases needed for OK library functionality.
  Use this in your module like: `use RivaAsh.ResultHelpers`
  """
  defmacro __using__(_opts) do
    quote do
      import OK, only: [success: 1, failure: 1]
      import OK.Only, only: [~>>: 2, ~>: 2]
      alias OK.Success
      alias OK.Failure
    end
  end
end
